﻿<Window x:Class="MEP.PowerBIM_6.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
        mc:Ignorable="d"
        Title="PowerBIM 6"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="White">

	<Window.Resources>
		<ResourceDictionary>
			<ResourceDictionary.MergedDictionaries>
				<ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
			</ResourceDictionary.MergedDictionaries>

			<!--  Converters  -->
			<!--<converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
			<converters:StatusColorConverter x:Key="StatusColorConverter" />-->
		</ResourceDictionary>
	</Window.Resources>

	<materialDesign:DialogHost>
		<Grid>
			<Grid.RowDefinitions>
				<RowDefinition Height="Auto"/>
				<!-- Header -->
				<RowDefinition Height="*"/>
				<!-- Main Content -->
				<RowDefinition Height="Auto"/>
				<!-- Status Bar -->
			</Grid.RowDefinitions>

			<!-- Header with Logo and Title -->
			<materialDesign:ColorZone Grid.Row="0"
                                      Mode="PrimaryMid"
                                      Padding="16"
                                      materialDesign:ElevationAssist.Elevation="Dp4">
				<StackPanel Orientation="Horizontal">
					<Image Source="../Resources/Images/BecaLogoBlack.png"
                           Height="32"
                           Margin="0,0,16,0"
                           VerticalAlignment="Center"/>
					<TextBlock Text="PowerBIM 6"
                               Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                               VerticalAlignment="Center"
                               Foreground="White"/>
					<TextBlock Text="- Electrical Design Analysis Tool"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               VerticalAlignment="Center"
                               Foreground="White"
                               Opacity="0.8"
                               Margin="8,0,0,0"/>
				</StackPanel>
			</materialDesign:ColorZone>

			<!-- Main Tabbed Interface -->
			<TabControl Grid.Row="1"
                        Style="{StaticResource MaterialDesignNavigationRailTabControl}"
                        Margin="8">

				<!-- Distribution Board Overview Tab -->
				<TabItem Header="Distribution Board Overview">
					<Grid Margin="16">
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="2*"/>
							<ColumnDefinition Width="*"/>
						</Grid.ColumnDefinitions>

						<!-- Distribution Board List with Status -->
						<materialDesign:Card Grid.Column="0"
                                             Margin="0,0,8,0"
                                             materialDesign:ElevationAssist.Elevation="Dp2">
							<Grid>
								<Grid.RowDefinitions>
									<RowDefinition Height="Auto"/>
									<RowDefinition Height="*"/>
								</Grid.RowDefinitions>

								<TextBlock Grid.Row="0"
                                           Text="Distribution Board Summary"
                                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                           Margin="16,16,16,8"/>

								<DataGrid Grid.Row="1"
                                          ItemsSource="{Binding DistributionBoards}"
                                          SelectedItem="{Binding SelectedDistributionBoard}"
                                          AutoGenerateColumns="False"
                                          CanUserAddRows="False"
                                          CanUserDeleteRows="False"
                                          Margin="16,0,16,16"
                                          Style="{StaticResource MaterialDesignDataGrid}">
									<DataGrid.Columns>
										<DataGridTextColumn Header="Distribution Board Name"
                                                            Binding="{Binding Name}"
                                                            IsReadOnly="True"
                                                            Width="*"/>
										<DataGridTextColumn Header="Circuits"
                                                            Binding="{Binding CircuitCount}"
                                                            IsReadOnly="True"
                                                            Width="80"/>
										<DataGridTextColumn Header="Status"
                                                            Binding="{Binding Status}"
                                                            IsReadOnly="True"
                                                            Width="100"/>
										<DataGridCheckBoxColumn Header="Locked"
                                                                Binding="{Binding IsLocked}"
                                                                IsReadOnly="True"
                                                                Width="60"/>
									</DataGrid.Columns>
								</DataGrid>
							</Grid>
						</materialDesign:Card>

						<!-- Distribution Board Actions Panel -->
						<materialDesign:Card Grid.Column="1"
                                             Margin="8,0,0,0"
                                             materialDesign:ElevationAssist.Elevation="Dp2">
							<StackPanel Margin="16">
								<TextBlock Text="Distribution Board Actions"
                                           Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                           Margin="0,0,0,16"/>

								<Button Content="Run Calculations"
                                        Command="{Binding RunCalculationsCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding CanRunCalculations}"/>

								<Button Content="Enhanced Circuit Edit"
                                        Command="{Binding OpenCircuitEditCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasSelectedDistributionBoard}"/>

								<Button Content="Distribution Board Edit"
                                        Command="{Binding OpenDbEditCommand}"
                                        Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasSelectedDistributionBoard}"/>

								<Separator Margin="0,16"/>

								<Button Content="Export Data"
                                        Command="{Binding ExportDataCommand}"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,4"/>

								<Button Content="Advanced Settings"
                                        Command="{Binding OpenAdvancedSettingsCommand}"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,4"/>

								<Separator Margin="0,16"/>

								<Button Content="Save Project"
                                        Command="{Binding SaveProjectCommand}"
                                        Style="{StaticResource MaterialDesignRaisedAccentButton}"
                                        Margin="0,4"
                                        IsEnabled="{Binding HasUnsavedChanges}"/>
							</StackPanel>
						</materialDesign:Card>
					</Grid>
				</TabItem>

				<!-- Project Settings Tab -->
				<TabItem Header="Project Settings">
					<materialDesign:Card Margin="16"
                                         materialDesign:ElevationAssist.Elevation="Dp2">
						<StackPanel Margin="16">
							<TextBlock Text="Project Configuration"
                                       Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                       Margin="0,0,0,16"/>

							<!-- Project settings controls will be added here -->
							<TextBlock Text="Project settings will be implemented in the next phase"
                                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                                       Foreground="Gray"/>
						</StackPanel>
					</materialDesign:Card>
				</TabItem>

				<!-- Bulk Operations Tab -->
				<TabItem Header="Bulk Operations">
					<materialDesign:Card Margin="16"
                                         materialDesign:ElevationAssist.Elevation="Dp2">
						<StackPanel Margin="16">
							<TextBlock Text="Bulk Edit Operations"
                                       Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                       Margin="0,0,0,16"/>

							<!-- Bulk operations controls will be added here -->
							<TextBlock Text="Bulk operations will be implemented in the next phase"
                                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                                       Foreground="Gray"/>
						</StackPanel>
					</materialDesign:Card>
				</TabItem>
			</TabControl>

			<!-- Status Bar -->
			<StatusBar Grid.Row="2"
                       Background="{DynamicResource MaterialDesignPaper}"
                       materialDesign:ElevationAssist.Elevation="Dp1">
				<StatusBarItem>
					<TextBlock Text="{Binding StatusMessage}"
                               Margin="8,0"/>
				</StatusBarItem>
				<StatusBarItem HorizontalAlignment="Right">
					<StackPanel Orientation="Horizontal">
						<ProgressBar Value="{Binding Progress}"
                                     Width="100"
                                     Height="16"
                                     Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}"
                                     Margin="0,0,8,0"/>
						<TextBlock Text="{Binding Progress, StringFormat={}{0:F0}%}"
                                   Visibility="{Binding ShowProgress, Converter={StaticResource BoolToVisConverter}}"
                                   VerticalAlignment="Center"
                                   Margin="0,0,8,0"/>
					</StackPanel>
				</StatusBarItem>
			</StatusBar>
		</Grid>
	</materialDesign:DialogHost>
</Window>

