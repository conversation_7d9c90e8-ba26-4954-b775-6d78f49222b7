﻿<Window
    x:Class="MEP.PowerBIM_6.Views.PowerBIMAboutPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title=""
    Background="White"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Window.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel
            MaxWidth="600"
            Margin="24"
            HorizontalAlignment="Center">
            <!--  Logo and Title  -->
            <StackPanel Margin="0,0,0,32" HorizontalAlignment="Center">
                <materialDesign:PackIcon
                    Width="64"
                    Height="64"
                    Margin="0,0,0,16"
                    HorizontalAlignment="Center"
                    Foreground="{DynamicResource PrimaryHueMidBrush}"
                    Kind="Flash" />
                <TextBlock
                    Margin="0,0,0,8"
                    HorizontalAlignment="Center"
                    Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                    Text="PowerBIM 6" />
                <TextBlock
                    HorizontalAlignment="Center"
                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                    Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                    Text="Electrical Circuit Analysis Tool" />
            </StackPanel>

            <!--  Version Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Version Information" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="0,0,16,8"
                            FontWeight="SemiBold"
                            Text="Version:" />
                        <TextBlock
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="6.0.0.0 WPF" />

                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="0"
                            Margin="0,0,16,8"
                            FontWeight="SemiBold"
                            Text="Build Date:" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="2025-06-30" />

                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="0"
                            Margin="0,0,16,8"
                            FontWeight="SemiBold"
                            Text="Architecture:" />
                        <TextBlock
                            Grid.Row="2"
                            Grid.Column="1"
                            Margin="0,0,0,8"
                            Text="MVVM with Dependency Injection" />

                        <TextBlock
                            Grid.Row="3"
                            Grid.Column="0"
                            Margin="0,0,16,8"
                            FontWeight="SemiBold"
                            Text="Authors: <AUTHORS>
                        <TextBlock
                            Grid.Row="3"
                            Grid.Column="1"
                            Text="Tristan Balme, Harry Billinge, Firza Utama" />

                        <TextBlock
                            Grid.Row="4"
                            Grid.Column="0"
                            Margin="0,0,16,0"
                            FontWeight="SemiBold"
                            Text="Authorized by:" />
                        <TextBlock
                            Grid.Row="4"
                            Grid.Column="1"
                            Text="David Meyer" />

                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Description  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="About PowerBIM" />

                    <TextBlock Margin="0,0,0,16" TextWrapping="Wrap">
                        PowerBIM is a comprehensive electrical circuit analysis tool designed for Autodesk Revit.
                        This WPF version represents a complete modernization of the original WinForms application,
                        featuring improved user interface, better performance, and enhanced maintainability.
                    </TextBlock>

                    <TextBlock
                        Margin="0,0,0,8"
                        FontWeight="SemiBold"
                        Text="Key Features:" />
                    <StackPanel Margin="16,0,0,16">
                        <TextBlock Margin="0,0,0,4" Text="• Automatic cable sizing and selection" />
                        <TextBlock Margin="0,0,0,4" Text="• Voltage drop calculations" />
                        <TextBlock Margin="0,0,0,4" Text="• Circuit breaker analysis" />
                        <TextBlock Margin="0,0,0,4" Text="• Fault current calculations" />
                        <TextBlock Margin="0,0,0,4" Text="• Comprehensive reporting" />
                        <TextBlock Margin="0,0,0,4" Text="• Export to Excel/CSV" />
                        <TextBlock Text="• Modern WPF interface with Material Design" />
                    </StackPanel>

                    <TextBlock
                        Margin="0,0,0,8"
                        FontWeight="SemiBold"
                        Text="Standards Compliance:" />
                    <StackPanel Margin="16,0,0,0">
                        <TextBlock Margin="0,0,0,4" Text="• AS/NZS 3008 (Cable Selection)" />
                        <TextBlock Margin="0,0,0,4" Text="• AS/NZS 3000 (Wiring Rules)" />
                        <TextBlock Text="• IEC 60364 (Electrical Installations)" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!--  Company Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Built Form Automation Group" />

                    
                    <TextBlock Margin="0,0,0,16" TextWrapping="Wrap">
                        Beca is one of the largest independent engineering consultancies in the Asia-Pacific region,
                        providing engineering, environmental, and planning services across multiple sectors.
                    </TextBlock>

                    <TextBlock
                        Margin="0,0,0,8"
                        FontWeight="SemiBold"
                        Text="Contact Information:" />
                    <StackPanel Margin="16,0,0,0">
                        <TextBlock Margin="0,0,0,4" Text="Website: www.beca.com" />
                        <TextBlock Margin="0,0,0,4" Text="Email: <EMAIL>" />
                        <TextBlock Text="Support: For technical support, please contact Authors" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!--  Copyright  -->
            <materialDesign:Card Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Copyright and License" />

                    <TextBlock Margin="0,0,0,8" TextWrapping="Wrap">
                        © 2025 Beca. All rights reserved.
                    </TextBlock>

                    <TextBlock
                        FontSize="12"
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        TextWrapping="Wrap">
                        This software is proprietary to Beca and is protected by copyright law.
                        Unauthorized reproduction or distribution of this software, or any portion of it,
                        may result in severe civil and criminal penalties.
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Window>
