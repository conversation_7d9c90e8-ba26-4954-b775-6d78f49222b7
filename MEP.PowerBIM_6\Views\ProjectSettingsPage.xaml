<Page
    x:Class="MEP.PowerBIM_6.Views.ProjectSettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Project Settings"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Page Header  -->
            <StackPanel Margin="0,0,0,24">
                <TextBlock
                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                    Text="Project Settings" />
                <TextBlock
                    Margin="0,4,0,0"
                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                    Text="Configure project-wide electrical calculation settings" />
            </StackPanel>

            <!--  Project Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Project Information" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBox
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="0,0,8,16"
                            materialDesign:HintAssist.Hint="Job Name"
                            Text="{Binding ProjectInfo.JobName}" />

                        <TextBox
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="8,0,0,16"
                            materialDesign:HintAssist.Hint="Job Number"
                            Text="{Binding ProjectInfo.JobNumber}" />

                        <TextBox
                            Grid.Row="1"
                            Grid.Column="0"
                            Margin="0,0,8,16"
                            materialDesign:HintAssist.Hint="Engineer"
                            Text="{Binding ProjectInfo.Engineer}" />

                        <TextBox
                            Grid.Row="1"
                            Grid.Column="1"
                            Margin="8,0,0,16"
                            materialDesign:HintAssist.Hint="Verifier"
                            Text="{Binding ProjectInfo.Verifier}" />

                        <TextBox
                            Grid.Row="2"
                            Grid.Column="0"
                            Grid.ColumnSpan="2"
                            Margin="0,0,0,16"
                            materialDesign:HintAssist.Hint="Project Address"
                            Text="{Binding ProjectInfo.ProjectAddress}" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Electrical Settings  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Electrical Settings" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  System Settings  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBox
                                Margin="0,0,0,16"
                                materialDesign:HintAssist.Hint="System Voltage (V)"
                                Text="{Binding ProjectInfo.SystemVoltage}" />

                            <TextBox
                                Margin="0,0,0,16"
                                materialDesign:HintAssist.Hint="System Frequency (Hz)"
                                Text="{Binding ProjectInfo.SystemFrequency}" />

                            <TextBox
                                Margin="0,0,0,16"
                                materialDesign:HintAssist.Hint="Ambient Temperature (°C)"
                                Text="{Binding ProjectInfo.AmbientTemperature}" />
                        </StackPanel>

                        <!--  Calculation Settings  -->
                        <StackPanel Grid.Column="1">
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock
                                    Margin="0,0,0,8"
                                    Text="Maximum Voltage Drop:" />
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton
                                        Margin="0,0,16,0"
                                        Content="5%"
                                        IsChecked="{Binding ProjectInfo.SystemVD5Percent}" />
                                    <RadioButton
                                        Content="7%"
                                        IsChecked="{Binding ProjectInfo.SystemVD7Percent}" />
                                </StackPanel>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,16">
                                <TextBlock
                                    Margin="0,0,0,8"
                                    Text="Cable Selection Standard:" />
                                <StackPanel Orientation="Horizontal">
                                    <RadioButton
                                        Margin="0,0,16,0"
                                        Content="NZ (30°C)"
                                        IsChecked="{Binding ProjectInfo.NZCableSelection}" />
                                    <RadioButton
                                        Content="AUS (40°C)"
                                        IsChecked="{Binding ProjectInfo.AUSCableSelection}" />
                                </StackPanel>
                            </StackPanel>

                            <CheckBox
                                Margin="0,0,0,8"
                                Content="Enable Automatic Cable Sizing"
                                IsChecked="{Binding ProjectInfo.AutoCableSizing}" />

                            <CheckBox
                                Content="Enable Automatic Breaker Sizing"
                                IsChecked="{Binding ProjectInfo.AutoBreakerSizing}" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Diversity Factors  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Diversity Factors" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Lighting Diversity  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Lighting Circuits" />

                            <TextBox
                                Margin="0,0,0,8"
                                materialDesign:HintAssist.Hint="Lighting Diversity Factor (%)"
                                Text="{Binding ProjectInfo.LightingDiversityFactor}" />

                            <TextBox
                                Margin="0,0,0,8"
                                materialDesign:HintAssist.Hint="Emergency Lighting Factor (%)"
                                Text="{Binding ProjectInfo.EmergencyLightingFactor}" />
                        </StackPanel>

                        <!--  Power Diversity  -->
                        <StackPanel Grid.Column="1">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Power Circuits" />

                            <TextBox
                                Margin="0,0,0,8"
                                materialDesign:HintAssist.Hint="GPO Diversity Factor (%)"
                                Text="{Binding ProjectInfo.GPODiversityFactor}" />

                            <TextBox
                                Margin="0,0,0,8"
                                materialDesign:HintAssist.Hint="Motor Diversity Factor (%)"
                                Text="{Binding ProjectInfo.MotorDiversityFactor}" />

                            <TextBox
                                materialDesign:HintAssist.Hint="HVAC Diversity Factor (%)"
                                Text="{Binding ProjectInfo.HVACDiversityFactor}" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Action Buttons  -->
            <StackPanel Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding SaveSettingsCommand}"
                    Content="Save Settings" />
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding ResetToDefaultsCommand}"
                    Content="Reset to Defaults" />
                <Button
                    Command="{Binding ImportSettingsCommand}"
                    Content="Import Settings" />
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Page>
