﻿<Window x:Class="MEP.PowerBIM_6.Views.CircuitEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
        Title="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}"
        Height="900"
        Width="1600"
        WindowState="Maximized"
        WindowStartupLocation="CenterOwner"
        Background="White">

	<Window.Resources>
		<ResourceDictionary>
			<ResourceDictionary.MergedDictionaries>
				<ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
				<ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
				<ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
				<ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.DeepPurple.xaml" />
				<ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" />
			</ResourceDictionary.MergedDictionaries>

			<!--<converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
			<converters:StatusColorConverter x:Key="StatusColorConverter" />-->

			<!-- DataGrid Cell Style for validation results -->
			<Style x:Key="ValidationCellStyle" TargetType="DataGridCell">
				<Style.Triggers>
					<DataTrigger Binding="{Binding CircuitCheckResult}" Value="PASS">
						<Setter Property="Background" Value="LightGreen"/>
					</DataTrigger>
					<DataTrigger Binding="{Binding CircuitCheckResult}" Value="FAIL">
						<Setter Property="Background" Value="LightCoral"/>
					</DataTrigger>
					<DataTrigger Binding="{Binding CircuitCheckResult}" Value="WARNING">
						<Setter Property="Background" Value="LightYellow"/>
					</DataTrigger>
				</Style.Triggers>
			</Style>
		</ResourceDictionary>
	</Window.Resources>

	<materialDesign:DialogHost>
		<Grid>
			<Grid.RowDefinitions>
				<RowDefinition Height="Auto"/>
				<!-- Header & Toolbar -->
				<RowDefinition Height="*"/>
				<!-- Main Content -->
				<RowDefinition Height="Auto"/>
				<!-- Phase Summary -->
				<RowDefinition Height="Auto"/>
				<!-- Action Buttons -->
			</Grid.RowDefinitions>

			<!-- Header & Toolbar -->
			<materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid">
				<Grid>
					<Grid.ColumnDefinitions>
						<ColumnDefinition Width="*"/>
						<ColumnDefinition Width="Auto"/>
					</Grid.ColumnDefinitions>

					<!-- Title -->
					<StackPanel Grid.Column="0" Orientation="Horizontal" Margin="16,8">
						<Image Source="../BecaLogoBlack.png" Height="24" Margin="0,0,8,0"/>
						<TextBlock Text="{Binding DistributionBoardName, StringFormat='Live Circuit Editor: {0}'}"
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Foreground="White" VerticalAlignment="Center"/>
					</StackPanel>

					<!-- Toolbar -->
					<StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,8">
						<Button Content="{Binding AutoCalculateButtonText}"
                                Command="{Binding ToggleAutoCalculateCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="4"/>
						<Button Content="Activate Path Edit"
                                Command="{Binding ActivatePathEditCommand}"
                                Style="{StaticResource MaterialDesignRaisedAccentButton}"
                                Margin="4"/>
						<Button Content="Refresh"
                                Command="{Binding RefreshFromRevitCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="4"/>
					</StackPanel>
				</Grid>
			</materialDesign:ColorZone>

			<!-- Main Content Area -->
			<Grid Grid.Row="1">
				<Grid.RowDefinitions>
					<RowDefinition Height="Auto"/>
					<!-- Search Bar -->
					<RowDefinition Height="*"/>
					<!-- DataGrid -->
				</Grid.RowDefinitions>

				<!-- Search Bar -->
				<materialDesign:Card Grid.Row="0" Margin="16,16,16,8">
					<Grid Margin="16,8">
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="*"/>
							<ColumnDefinition Width="Auto"/>
						</Grid.ColumnDefinitions>

						<TextBox Grid.Column="0"
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 materialDesign:HintAssist.Hint="Search circuits by number or description..."
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"/>

						<Button Grid.Column="1" Content="Clear"
                                Command="{Binding ClearSearchCommand}"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="8,0,0,0"/>
					</Grid>
				</materialDesign:Card>

				<!-- Main Circuit DataGrid -->
				<DataGrid Grid.Row="1"
                          ItemsSource="{Binding CircuitData}"
                          SelectedItem="{Binding SelectedCircuit}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Margin="16,0,16,8"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          ScrollViewer.CanContentScroll="True"
                          ScrollViewer.HorizontalScrollBarVisibility="Auto"
                          ScrollViewer.VerticalScrollBarVisibility="Auto">

					<DataGrid.Columns>
						<!-- Circuit Number -->
						<DataGridTextColumn Header="Circuit #"
                                            Binding="{Binding CircuitNumber}"
                                            IsReadOnly="True"
                                            Width="80"
                                            CellStyle="{StaticResource ValidationCellStyle}"/>

						<!-- Description -->
						<DataGridTextColumn Header="Description"
                                            Binding="{Binding Description}"
                                            Width="150"/>

						<!-- Device Rating -->
						<DataGridComboBoxColumn Header="Device Rating"
                                                SelectedItemBinding="{Binding DeviceRating}"
                                                ItemsSource="{Binding DataContext.DeviceRatings, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/>

						<!-- Device Curve Type -->
						<DataGridComboBoxColumn Header="Curve Type"
                                                SelectedItemBinding="{Binding DeviceCurveType}"
                                                ItemsSource="{Binding DataContext.CurveTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="80"/>

						<!-- Protection Device -->
						<DataGridComboBoxColumn Header="Protection Device"
                                                SelectedItemBinding="{Binding ProtectionDevice}"
                                                ItemsSource="{Binding DataContext.ProtectionDevices, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/>

						<!-- RCD Protection -->
						<DataGridComboBoxColumn Header="RCD Protection"
                                                SelectedItemBinding="{Binding RcdProtection}"
                                                ItemsSource="{Binding DataContext.RcdProtectionOptions, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/>

						<!-- Other Controls -->
						<DataGridTextColumn Header="Other Controls"
                                            Binding="{Binding OtherControls}"
                                            Width="120"/>

						<!-- Cable to First -->
						<DataGridComboBoxColumn Header="Cable to First"
                                                SelectedItemBinding="{Binding CableToFirst}"
                                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/>

						<!-- Cable to Remainder -->
						<DataGridComboBoxColumn Header="Cable to Remainder"
                                                SelectedItemBinding="{Binding CableToRemainder}"
                                                ItemsSource="{Binding DataContext.CableTypes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/>

						<!-- Installation Method -->
						<DataGridComboBoxColumn Header="Installation Method"
                                                SelectedItemBinding="{Binding InstallationMethod}"
                                                ItemsSource="{Binding DataContext.InstallationMethods, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="120"/>

						<!-- Derating Factor -->
						<DataGridTextColumn Header="Derating Factor"
                                            Binding="{Binding DeratingFactor, StringFormat=N2}"
                                            Width="100"/>

						<!-- Diversity -->
						<DataGridTextColumn Header="Diversity"
                                            Binding="{Binding Diversity, StringFormat=N2}"
                                            Width="80"/>

						<!-- Manual Override -->
						<DataGridCheckBoxColumn Header="Manual"
                                                Binding="{Binding IsManual}"
                                                Width="60"/>

						<!-- Path Mode -->
						<DataGridComboBoxColumn Header="Path Mode"
                                                SelectedItemBinding="{Binding PathMode}"
                                                ItemsSource="{Binding DataContext.PathModes, RelativeSource={RelativeSource AncestorType=Window}}"
                                                Width="100"/>

						<!-- Set Path Button -->
						<DataGridTemplateColumn Header="Set Path" Width="80">
							<DataGridTemplateColumn.CellTemplate>
								<DataTemplate>
									<Button Content="Set Path"
                                            Command="{Binding DataContext.SetPathCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            IsEnabled="{Binding CanEditPath}"
                                            FontSize="10"
                                            Padding="4,2"/>
								</DataTemplate>
							</DataGridTemplateColumn.CellTemplate>
						</DataGridTemplateColumn>

						<!-- Length to First -->
						<DataGridTextColumn Header="Length to First"
                                            Binding="{Binding LengthToFirst, StringFormat=N2}"
                                            Width="100"/>

						<!-- Length to Final -->
						<DataGridTextColumn Header="Total Length"
                                            Binding="{Binding LengthToFinal, StringFormat=N2}"
                                            Width="100"/>

						<!-- Manual Current Checkbox -->
						<DataGridCheckBoxColumn Header="Manual Current"
                                                Binding="{Binding ManualCurrent}"
                                                Width="100"/>

						<!-- Current Value -->
						<DataGridTextColumn Header="Current (A)"
                                            Binding="{Binding Current, StringFormat=N2}"
                                            IsReadOnly="True"
                                            Width="80"/>

						<!-- Number of Elements Button -->
						<DataGridTemplateColumn Header="Elements" Width="80">
							<DataGridTemplateColumn.CellTemplate>
								<DataTemplate>
									<Button Content="{Binding NumberOfElements}"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            FontSize="10"
                                            Padding="4,2"/>
								</DataTemplate>
							</DataGridTemplateColumn.CellTemplate>
						</DataGridTemplateColumn>

						<!-- Validation Results Columns -->
						<DataGridTextColumn Header="Trip Rating Check"
                                            Binding="{Binding CheckTripRating}"
                                            IsReadOnly="True"
                                            Width="120"/>

						<DataGridTextColumn Header="Cable 1 Valid"
                                            Binding="{Binding Cable1Valid}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 2 Valid"
                                            Binding="{Binding Cable2Valid}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="CPD Discriminates"
                                            Binding="{Binding CheckCpdDiscriminates}"
                                            IsReadOnly="True"
                                            Width="120"/>

						<DataGridTextColumn Header="Load Current Check"
                                            Binding="{Binding CheckLoadCurrent}"
                                            IsReadOnly="True"
                                            Width="120"/>

						<DataGridTextColumn Header="Cable 1 Current"
                                            Binding="{Binding CheckCable1Current}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 2 Current"
                                            Binding="{Binding CheckCable2Current}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 1 VD%"
                                            Binding="{Binding CheckCable1VoltageDropPercent}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 2 VD%"
                                            Binding="{Binding CheckCable2VoltageDropPercent}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 1 SC"
                                            Binding="{Binding CheckCable1ScWithstand}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Cable 2 SC"
                                            Binding="{Binding CheckCable2ScWithstand}"
                                            IsReadOnly="True"
                                            Width="100"/>

						<DataGridTextColumn Header="Check Summary"
                                            Binding="{Binding CircuitCheckSummary}"
                                            IsReadOnly="True"
                                            Width="150"/>

						<DataGridTextColumn Header="Check Result"
                                            Binding="{Binding CircuitCheckResult}"
                                            IsReadOnly="True"
                                            Width="100"
                                            CellStyle="{StaticResource ValidationCellStyle}"/>

						<DataGridTextColumn Header="Revision"
                                            Binding="{Binding CircuitRevision}"
                                            Width="80"/>

						<DataGridCheckBoxColumn Header="Spare/Space"
                                                Binding="{Binding IsSpareOrSpace}"
                                                Width="80"/>
					</DataGrid.Columns>
				</DataGrid>
			</Grid>

			<!-- Phase Loading Summary -->
			<Grid Grid.Row="2" Margin="16,8">
				<Grid.ColumnDefinitions>
					<ColumnDefinition Width="*"/>
					<ColumnDefinition Width="20"/>
					<ColumnDefinition Width="*"/>
				</Grid.ColumnDefinitions>

				<!-- Diversified Phase Loading -->
				<materialDesign:Card Grid.Column="0">
					<StackPanel Margin="16">
						<TextBlock Text="Diversified Phase Loading (Run PowerBIM)"
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,8"
                                   Background="{StaticResource PrimaryHueMidBrush}"
                                   Foreground="White"
                                   Padding="8,4"/>
						<DataGrid ItemsSource="{Binding DiversifiedPhaseData}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="None"
                                  Background="Transparent">
							<DataGrid.Columns>
								<DataGridTextColumn Header="Phase R" Binding="{Binding PhaseR, StringFormat=N2}" Width="*"/>
								<DataGridTextColumn Header="Phase W" Binding="{Binding PhaseW, StringFormat=N2}" Width="*"/>
								<DataGridTextColumn Header="Phase B" Binding="{Binding PhaseB, StringFormat=N2}" Width="*"/>
							</DataGrid.Columns>
						</DataGrid>
					</StackPanel>
				</materialDesign:Card>

				<!-- Un-Diversified Phase Loading -->
				<materialDesign:Card Grid.Column="2">
					<StackPanel Margin="16">
						<TextBlock Text="Un-Diversified Phase Loading"
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,8"
                                   Background="{StaticResource PrimaryHueMidBrush}"
                                   Foreground="White"
                                   Padding="8,4"/>
						<DataGrid ItemsSource="{Binding UnDiversifiedPhaseData}"
                                  AutoGenerateColumns="False"
                                  IsReadOnly="True"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="None"
                                  Background="Transparent">
							<DataGrid.Columns>
								<DataGridTextColumn Header="Revit Phase R" Binding="{Binding RevitPhaseR, StringFormat=N2}" Width="*"/>
								<DataGridTextColumn Header="Revit Phase W" Binding="{Binding RevitPhaseW, StringFormat=N2}" Width="*"/>
								<DataGridTextColumn Header="Revit Phase B" Binding="{Binding RevitPhaseB, StringFormat=N2}" Width="*"/>
							</DataGrid.Columns>
						</DataGrid>
					</StackPanel>
				</materialDesign:Card>
			</Grid>

			<!-- Action Buttons & Status -->
			<Grid Grid.Row="3" Background="{StaticResource MaterialDesignPaper}">
				<Grid.ColumnDefinitions>
					<ColumnDefinition Width="*"/>
					<ColumnDefinition Width="Auto"/>
				</Grid.ColumnDefinitions>

				<!-- Tips & Status -->
				<StackPanel Grid.Column="0" Margin="16,8" VerticalAlignment="Center">
					<TextBlock Text="{Binding TipsText}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               TextWrapping="Wrap"
                               Foreground="{StaticResource MaterialDesignBodyLight}"/>
					<TextBlock Text="{Binding StatusMessage}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{StaticResource PrimaryHueMidBrush}"
                               Margin="0,4,0,0"
                               Visibility="{Binding StatusMessage, Converter={StaticResource BoolToVisConverter}}"/>
				</StackPanel>

				<!-- Action Buttons -->
				<StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,8">
					<Button Content="Cancel"
                            Command="{Binding CancelCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="4"
                            Padding="16,8"/>
					<Button Content="Save"
                            Command="{Binding SaveCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Margin="4"
                            Padding="16,8"/>
				</StackPanel>
			</Grid>
		</Grid>
	</materialDesign:DialogHost>
</Window>

