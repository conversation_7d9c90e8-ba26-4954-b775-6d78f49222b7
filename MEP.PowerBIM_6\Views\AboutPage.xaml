<Page
    x:Class="MEP.PowerBIM_6.Views.AboutPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="About PowerBIM 6"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Header  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="32">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon
                        Width="64"
                        Height="64"
                        Margin="0,0,0,16"
                        HorizontalAlignment="Center"
                        Foreground="{DynamicResource PrimaryHueMidBrush}"
                        Kind="Flash" />
                    
                    <TextBlock
                        HorizontalAlignment="Center"
                        Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                        Text="PowerBIM 6" />
                    
                    <TextBlock
                        Margin="0,8,0,0"
                        HorizontalAlignment="Center"
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                        Text="Electrical Circuit Analysis and Cable Sizing Tool" />
                    
                    <TextBlock
                        Margin="0,16,0,0"
                        HorizontalAlignment="Center"
                        Text="Version 6.0.0" />
                </StackPanel>
            </materialDesign:Card>

            <!--  Description  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="About PowerBIM" />
                    
                    <TextBlock
                        Margin="0,0,0,12"
                        TextWrapping="Wrap">
                        PowerBIM 6 is a comprehensive electrical engineering tool designed for Autodesk Revit MEP. 
                        It provides advanced circuit analysis, automatic cable sizing, voltage drop calculations, 
                        and electrical code compliance checking for electrical design professionals.
                    </TextBlock>
                    
                    <TextBlock
                        Margin="0,0,0,12"
                        TextWrapping="Wrap">
                        This version represents a complete architectural modernization with WPF MVVM patterns, 
                        enhanced user interface, and improved calculation algorithms while maintaining compatibility 
                        with existing Revit projects.
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>

            <!--  Key Features  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Key Features" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock Margin="0,0,0,8" Text="• Automatic cable sizing and selection" />
                            <TextBlock Margin="0,0,0,8" Text="• Voltage drop calculations" />
                            <TextBlock Margin="0,0,0,8" Text="• Circuit protection coordination" />
                            <TextBlock Margin="0,0,0,8" Text="• Load diversity calculations" />
                            <TextBlock Margin="0,0,0,8" Text="• Electrical code compliance checking" />
                        </StackPanel>

                        <StackPanel Grid.Column="1">
                            <TextBlock Margin="0,0,0,8" Text="• Distribution board analysis" />
                            <TextBlock Margin="0,0,0,8" Text="• Bulk circuit operations" />
                            <TextBlock Margin="0,0,0,8" Text="• Export to Excel, PDF, and CSV" />
                            <TextBlock Margin="0,0,0,8" Text="• Project settings management" />
                            <TextBlock Margin="0,0,0,8" Text="• Modern WPF user interface" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Technical Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Technical Information" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Margin="0,0,16,8" FontWeight="SemiBold" Text="Framework:" />
                        <TextBlock Grid.Row="0" Grid.Column="1" Margin="0,0,0,8" Text=".NET Framework 4.8" />

                        <TextBlock Grid.Row="1" Grid.Column="0" Margin="0,0,16,8" FontWeight="SemiBold" Text="UI Technology:" />
                        <TextBlock Grid.Row="1" Grid.Column="1" Margin="0,0,0,8" Text="WPF with Material Design" />

                        <TextBlock Grid.Row="2" Grid.Column="0" Margin="0,0,16,8" FontWeight="SemiBold" Text="Architecture:" />
                        <TextBlock Grid.Row="2" Grid.Column="1" Margin="0,0,0,8" Text="MVVM with Dependency Injection" />

                        <TextBlock Grid.Row="3" Grid.Column="0" Margin="0,0,16,8" FontWeight="SemiBold" Text="Revit API:" />
                        <TextBlock Grid.Row="3" Grid.Column="1" Margin="0,0,0,8" Text="Thread-safe ExternalEvent pattern" />

                        <TextBlock Grid.Row="4" Grid.Column="0" Margin="0,0,16,8" FontWeight="SemiBold" Text="Standards:" />
                        <TextBlock Grid.Row="4" Grid.Column="1" Text="AS/NZS 3008, IEC 60364" />
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Company Information  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Beca Group" />
                    
                    <TextBlock
                        Margin="0,0,0,12"
                        TextWrapping="Wrap">
                        PowerBIM is developed by Beca Group, a leading engineering consultancy 
                        providing innovative solutions across infrastructure, buildings, and industrial sectors.
                    </TextBlock>
                    
                    <StackPanel Orientation="Horizontal">
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding OpenWebsiteCommand}"
                            Content="Visit Website"
                            Style="{StaticResource MaterialDesignFlatButton}" />
                        
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding ContactSupportCommand}"
                            Content="Contact Support"
                            Style="{StaticResource MaterialDesignFlatButton}" />
                        
                        <Button
                            Command="{Binding ViewDocumentationCommand}"
                            Content="Documentation"
                            Style="{StaticResource MaterialDesignFlatButton}" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!--  Copyright  -->
            <TextBlock
                HorizontalAlignment="Center"
                Foreground="{DynamicResource MaterialDesignBodyLight}"
                Text="© 2024 Beca Group. All rights reserved." />
        </StackPanel>
    </ScrollViewer>
</Page>
