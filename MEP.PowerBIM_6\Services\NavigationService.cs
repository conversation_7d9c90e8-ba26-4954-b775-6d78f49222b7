using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Navigation;
using BecaActivityLogger.CoreLogic.Data;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.Views;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace MEP.PowerBIM_6.Services
{
    /// <summary>
    /// Navigation service for PowerBIM 6 WPF application
    /// Manages page-based navigation within the MainFrame
    /// </summary>
    public class NavigationService : INavigationService
    {
        #region Fields

        private readonly IServiceProvider _serviceProvider;
        private readonly BecaActivityLoggerData _logger;
        private Frame _mainFrame;
        private readonly Dictionary<string, Type> _pageTypes;
        private readonly Dictionary<string, Page> _pageCache;
        private string _currentPageKey;

        #endregion

        #region Events

        /// <summary>
        /// Event raised when navigation occurs
        /// </summary>
        public event EventHandler<NavigationEventArgs> Navigated;

        /// <summary>
        /// Event raised when navigation fails
        /// </summary>
        public event EventHandler<MEP.PowerBIM_6.Services.NavigationFailedEventArgs> NavigationFailed;

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the navigation service
        /// </summary>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="logger">Activity logger</param>
        public NavigationService(IServiceProvider serviceProvider, BecaActivityLoggerData logger)
        {
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            _pageTypes = new Dictionary<string, Type>();
            _pageCache = new Dictionary<string, Page>();
            
            RegisterPages();
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Current page key
        /// </summary>
        public string CurrentPageKey => _currentPageKey;

        /// <summary>
        /// Whether navigation can go back
        /// </summary>
        public bool CanGoBack => _mainFrame?.CanGoBack ?? false;

        /// <summary>
        /// Whether navigation can go forward
        /// </summary>
        public bool CanGoForward => _mainFrame?.CanGoForward ?? false;

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the navigation service with the main frame
        /// </summary>
        /// <param name="mainFrame">Main frame for navigation</param>
        public void Initialize(Frame mainFrame)
        {
            _mainFrame = mainFrame ?? throw new ArgumentNullException(nameof(mainFrame));
            
            // Subscribe to frame navigation events
            _mainFrame.Navigated += OnFrameNavigated;
            _mainFrame.NavigationFailed += OnFrameNavigationFailed;
            
            _logger?.Log("NavigationService initialized", LogType.Information);
        }

        /// <summary>
        /// Navigate to a page by key
        /// </summary>
        /// <param name="pageKey">Page key to navigate to</param>
        /// <param name="parameter">Optional navigation parameter</param>
        /// <returns>True if navigation succeeded</returns>
        public bool NavigateTo(string pageKey, object parameter = null)
        {
            try
            {
                if (string.IsNullOrEmpty(pageKey))
                {
                    _logger?.Log("Page key is null or empty", LogType.Warning);
                    return false;
                }

                if (_mainFrame == null)
                {
                    _logger?.Log("Main frame not initialized", LogType.Error);
                    return false;
                }

                if (!_pageTypes.ContainsKey(pageKey))
                {
                    _logger?.Log($"Page key '{pageKey}' not registered", LogType.Error);
                    return false;
                }

                // Get or create page
                var page = GetOrCreatePage(pageKey);
                if (page == null)
                {
                    _logger?.Log($"Failed to create page for key '{pageKey}'", LogType.Error);
                    return false;
                }

                // Set navigation parameter if provided
                if (parameter != null && page.DataContext is BaseViewModel viewModel)
                {
                    viewModel.NavigationParameter = parameter;
                }

                // Navigate to page
                var result = _mainFrame.Navigate(page);
                if (result)
                {
                    _currentPageKey = pageKey;
                    _logger?.Log($"Navigated to page: {pageKey}", LogType.Information);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Navigation failed: {ex.Message}", LogType.Error);
                OnNavigationFailed(pageKey, ex);
                return false;
            }
        }

        /// <summary>
        /// Go back in navigation history
        /// </summary>
        /// <returns>True if navigation succeeded</returns>
        public bool GoBack()
        {
            try
            {
                if (_mainFrame?.CanGoBack == true)
                {
                    _mainFrame.GoBack();
                    _logger?.Log("Navigated back", LogType.Information);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Go back failed: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Go forward in navigation history
        /// </summary>
        /// <returns>True if navigation succeeded</returns>
        public bool GoForward()
        {
            try
            {
                if (_mainFrame?.CanGoForward == true)
                {
                    _mainFrame.GoForward();
                    _logger?.Log("Navigated forward", LogType.Information);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Go forward failed: {ex.Message}", LogType.Error);
                return false;
            }
        }

        /// <summary>
        /// Clear navigation history
        /// </summary>
        public void ClearHistory()
        {
            try
            {
                _mainFrame?.NavigationService?.RemoveBackEntry();
                _logger?.Log("Navigation history cleared", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Clear history failed: {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Clear page cache
        /// </summary>
        public void ClearCache()
        {
            try
            {
                _pageCache.Clear();
                _logger?.Log("Page cache cleared", LogType.Information);
            }
            catch (Exception ex)
            {
                _logger?.Log($"Clear cache failed: {ex.Message}", LogType.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Register all available pages
        /// </summary>
        private void RegisterPages()
        {
            // Register page types with their keys
            _pageTypes.Add(PageKeys.Home, typeof(HomePage));
            _pageTypes.Add(PageKeys.DistributionBoards, typeof(DistributionBoardsPage));
            _pageTypes.Add(PageKeys.Circuits, typeof(CircuitsPage));
            _pageTypes.Add(PageKeys.BulkOperations, typeof(BulkOperationsPage));
            _pageTypes.Add(PageKeys.ProjectSettings, typeof(ProjectSettingsPage));
            _pageTypes.Add(PageKeys.Export, typeof(ExportPage));
            _pageTypes.Add(PageKeys.About, typeof(AboutPage));
            
            _logger?.Log($"Registered {_pageTypes.Count} page types", LogType.Information);
        }

        /// <summary>
        /// Get or create a page instance
        /// </summary>
        /// <param name="pageKey">Page key</param>
        /// <returns>Page instance</returns>
        private Page GetOrCreatePage(string pageKey)
        {
            try
            {
                // Check cache first
                if (_pageCache.ContainsKey(pageKey))
                {
                    return _pageCache[pageKey];
                }

                // Create new page instance
                var pageType = _pageTypes[pageKey];
                var page = (Page)Activator.CreateInstance(pageType);
                
                // Set DataContext if page has corresponding ViewModel
                SetPageDataContext(page, pageKey);
                
                // Cache the page
                _pageCache[pageKey] = page;
                
                return page;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to create page '{pageKey}': {ex.Message}", LogType.Error);
                return null;
            }
        }

        /// <summary>
        /// Set the DataContext for a page
        /// </summary>
        /// <param name="page">Page instance</param>
        /// <param name="pageKey">Page key</param>
        private void SetPageDataContext(Page page, string pageKey)
        {
            try
            {
                BaseViewModel viewModel = pageKey switch
                {
                    PageKeys.Home => GetMainViewModel(),
                    PageKeys.DistributionBoards => GetMainViewModel(),
                    PageKeys.Circuits => _serviceProvider.GetRequiredService<CircuitEditViewModel>(),
                    PageKeys.BulkOperations => GetMainViewModel(),
                    PageKeys.ProjectSettings => _serviceProvider.GetRequiredService<AdvancedSettingsViewModel>(),
                    PageKeys.Export => _serviceProvider.GetRequiredService<ExportViewModel>(),
                    _ => null
                };

                if (viewModel != null)
                {
                    page.DataContext = viewModel;
                }
            }
            catch (Exception ex)
            {
                _logger?.Log($"Failed to set DataContext for page '{pageKey}': {ex.Message}", LogType.Error);
            }
        }

        /// <summary>
        /// Get the initialized MainViewModel from the main window
        /// </summary>
        /// <returns>Initialized MainViewModel or new instance if not available</returns>
        private MainViewModel GetMainViewModel()
        {
            try
            {
                // Try to get the MainViewModel from the main window's DataContext
                if (_mainFrame?.Parent is FrameworkElement parent)
                {
                    // Walk up the visual tree to find the main window
                    var window = Window.GetWindow(parent);
                    if (window?.DataContext is MainViewModel mainViewModel)
                    {
                        _logger?.Log($"Found initialized MainViewModel with {mainViewModel.DistributionBoards?.Count ?? 0} distribution boards", LogType.Information);
                        return mainViewModel;
                    }
                }

                // Fallback: get singleton instance from DI container
                _logger?.Log("Getting MainViewModel from DI container", LogType.Information);
                var viewModel = _serviceProvider.GetRequiredService<MainViewModel>();
                _logger?.Log($"MainViewModel from DI has {viewModel.DistributionBoards?.Count ?? 0} distribution boards", LogType.Information);
                return viewModel;
            }
            catch (Exception ex)
            {
                _logger?.Log($"Error getting MainViewModel: {ex.Message}", LogType.Error);
                return _serviceProvider.GetRequiredService<MainViewModel>();
            }
        }

        /// <summary>
        /// Handle frame navigation events
        /// </summary>
        private void OnFrameNavigated(object sender, NavigationEventArgs e)
        {
            Navigated?.Invoke(this, e);
        }

        /// <summary>
        /// Handle frame navigation failed events
        /// </summary>
        private void OnFrameNavigationFailed(object sender, System.Windows.Navigation.NavigationFailedEventArgs e)
        {
            // Convert WPF NavigationFailedEventArgs to our custom NavigationFailedEventArgs
            var customArgs = new NavigationFailedEventArgs(
                e.Uri?.ToString() ?? "Unknown",
                e.Exception);
            NavigationFailed?.Invoke(this, customArgs);
        }

        /// <summary>
        /// Raise navigation failed event
        /// </summary>
        private void OnNavigationFailed(string pageKey, Exception exception)
        {
            var args = new NavigationFailedEventArgs(pageKey, exception);
            NavigationFailed?.Invoke(this, args);
        }

        #endregion
    }

    /// <summary>
    /// Page keys for navigation
    /// </summary>
    public static class PageKeys
    {
        public const string Home = "Home";
        public const string DistributionBoards = "DistributionBoards";
        public const string Circuits = "Circuits";
        public const string BulkOperations = "BulkOperations";
        public const string ProjectSettings = "ProjectSettings";
        public const string Export = "Export";
        public const string About = "About";
    }

    /// <summary>
    /// Custom NavigationFailedEventArgs for navigation service
    /// </summary>
    public class NavigationFailedEventArgs : EventArgs
    {
        /// <summary>
        /// Page key that failed to navigate
        /// </summary>
        public string PageKey { get; }

        /// <summary>
        /// Exception that caused the navigation failure
        /// </summary>
        public Exception Exception { get; }

        /// <summary>
        /// Initialize NavigationFailedEventArgs
        /// </summary>
        /// <param name="pageKey">Page key that failed</param>
        /// <param name="exception">Exception that occurred</param>
        public NavigationFailedEventArgs(string pageKey, Exception exception)
        {
            PageKey = pageKey;
            Exception = exception;
        }
    }
}
