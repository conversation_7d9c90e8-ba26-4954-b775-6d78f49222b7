using System;
using System.Windows.Controls;
using System.Windows.Navigation;

namespace MEP.PowerBIM_6.Services.Interfaces
{
    /// <summary>
    /// Interface for navigation service
    /// Provides page-based navigation capabilities for PowerBIM 6 WPF application
    /// </summary>
    public interface INavigationService
    {
        #region Events

        /// <summary>
        /// Event raised when navigation occurs
        /// </summary>
        event EventHandler<NavigationEventArgs> Navigated;

        /// <summary>
        /// Event raised when navigation fails
        /// </summary>
        event EventHandler<MEP.PowerBIM_6.Services.NavigationFailedEventArgs> NavigationFailed;

        #endregion

        #region Properties

        /// <summary>
        /// Current page key
        /// </summary>
        string CurrentPageKey { get; }

        /// <summary>
        /// Whether navigation can go back
        /// </summary>
        bool CanGoBack { get; }

        /// <summary>
        /// Whether navigation can go forward
        /// </summary>
        bool CanGoForward { get; }

        #endregion

        #region Methods

        /// <summary>
        /// Initialize the navigation service with the main frame
        /// </summary>
        /// <param name="mainFrame">Main frame for navigation</param>
        void Initialize(Frame mainFrame);

        /// <summary>
        /// Navigate to a page by key
        /// </summary>
        /// <param name="pageKey">Page key to navigate to</param>
        /// <param name="parameter">Optional navigation parameter</param>
        /// <returns>True if navigation succeeded</returns>
        bool NavigateTo(string pageKey, object parameter = null);

        /// <summary>
        /// Go back in navigation history
        /// </summary>
        /// <returns>True if navigation succeeded</returns>
        bool GoBack();

        /// <summary>
        /// Go forward in navigation history
        /// </summary>
        /// <returns>True if navigation succeeded</returns>
        bool GoForward();

        /// <summary>
        /// Clear navigation history
        /// </summary>
        void ClearHistory();

        /// <summary>
        /// Clear page cache
        /// </summary>
        void ClearCache();

        #endregion
    }
}
