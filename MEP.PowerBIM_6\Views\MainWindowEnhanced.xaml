﻿<Window
    x:Class="MEP.PowerBIM_6.Views.MainWindowEnhanced"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Beca Tools | MEP | Electrical"
    Width="1400"
    Height="900"
    MinWidth="1200"
    MinHeight="700"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    Closing="Window_Closing"
    WindowStartupLocation="CenterScreen"
    WindowState="Normal"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Converters  -->
            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header with Navigation  -->
        <Border
            Grid.Row="0"
            Padding="16,12"
            Background="{DynamicResource PrimaryHueMidBrush}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Title  -->
                <StackPanel
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <materialDesign:PackIcon
                        Width="24"
                        Height="24"
                        Margin="0,0,8,0"
                        VerticalAlignment="Center"
                        Foreground="White"
                        Kind="Flash" />
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="18"
                        FontWeight="SemiBold"
                        Foreground="White"
                        Text="PowerBIM 6" />
                </StackPanel>

                <!--  Navigation Buttons  -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button
                        x:Name="btnHome"
                        Margin="4,0"
                        Click="NavigateToHome"
                        Content="Home"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignFlatButton}" />
                    <Button
                        x:Name="btnSettings"
                        Margin="4,0"
                        Click="NavigateToSettings"
                        Content="Help and Documentation"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignFlatButton}" />
                    <Button
                        x:Name="btnAbout"
                        Margin="4,0"
                        Click="NavigateToAbout"
                        Content="About"
                        Foreground="White"
                        Style="{StaticResource MaterialDesignFlatButton}" />
                </StackPanel>
            </Grid>
        </Border>

        <!--  Quick Actions Toolbar  -->
        <Border
            Grid.Row="1"
            Padding="16,8"
            Background="{DynamicResource MaterialDesignDivider}">
            <StackPanel HorizontalAlignment="Left" Orientation="Horizontal">
                <Button Margin="0,0,10,0"
                    x:Name="btnLoadData"
                    Command="{Binding LoadProjectDataCommand}"
                    Content="Load Data"
                    ToolTip="Load project data from Revit" />
                <Button Margin="0,0,10,0"
                    x:Name="btnRunSizer"
                    Command="{Binding RunAutoSizerCommand}"
                    Content="Run Auto Sizer"
                    ToolTip="Run automatic cable and breaker sizing" />
                <Button
                    x:Name="btnSave"
                    Command="{Binding SaveCommand}"
                    Content="Save"
                    ToolTip="Save results to Revit" />
                <!--<Button
                    x:Name="btnExport"
                    Command="{Binding ExportCommand}"
                    Content="Export"
                    ToolTip="Export to Excel/CSV" />
                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                <Button
                    x:Name="btnSelectAll"
                    Command="{Binding SelectAllDBsCommand}"
                    Content="Select All"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="Select all distribution boards" />
                <Button
                    x:Name="btnSelectNone"
                    Command="{Binding SelectNoneDBsCommand}"
                    Content="Select None"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="Deselect all distribution boards" />
                <Button
                    x:Name="btnRefresh"
                    Command="{Binding RefreshCommand}"
                    Content="Refresh"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    ToolTip="Refresh data from Revit" />-->
            </StackPanel>
        </Border>

        <!--  Main Content Area with Navigation  -->
        <Grid Grid.Row="2" Margin="8">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200" />
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Navigation Panel  -->
            <Border
                Grid.Column="0"
                Margin="0,0,4,0"
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="4">
                <StackPanel Margin="8">
                    <TextBlock Margin="0,0,0,16" Text="Navigation" />

                    <ListBox
                        x:Name="NavigationList"
                        SelectionChanged="NavigationList_SelectionChanged"
                        Style="{StaticResource MaterialDesignListBox}">
                        <ListBoxItem
                            x:Name="navHome"
                            Content="🏠 Home"
                            IsSelected="True" />
                        <ListBoxItem x:Name="navDistributionBoards" Content="⚡ Distribution Boards" />
                        <ListBoxItem x:Name="navCircuits" Content="⚙️ MCC" />
                        <ListBoxItem x:Name="navResults" Content="🗂️ Bulk Data Entry" />
                        <ListBoxItem x:Name="navSettings" Content="🎚️ PB Settings" />
                    </ListBox>

                    <!--  Project Summary  -->
                    <Border
                        Margin="0,16,0,0"
                        Padding="8"
                        Background="{DynamicResource MaterialDesignDivider}"
                        CornerRadius="4">
                        <StackPanel>
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Project Summary" />
                            <TextBlock>
                                <Run Text="Total DBs: " />
                                <Run FontWeight="SemiBold" Text="{Binding DistributionBoards.Count, Mode=OneWay, FallbackValue=0}" />
                            </TextBlock>
                            <TextBlock>
                                <Run Text="Selected: " />
                                <Run FontWeight="SemiBold" Text="{Binding SelectedDBCount,Mode=OneWay, FallbackValue=0}" />
                            </TextBlock>
                            <TextBlock>
                                <Run Text="Total Circuits: " />
                                <Run FontWeight="SemiBold" Text="{Binding TotalCircuitCount,Mode=OneWay, FallbackValue=0}" />
                            </TextBlock>
                            <StackPanel Margin="0,4,0,0" Orientation="Horizontal">
                                <TextBlock Margin="0,0,4,0" Text="✅" />
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="Green"
                                    Text="{Binding TotalPassCount, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock Margin="8,0,4,0" Text="⚠️" />
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="Orange"
                                    Text="{Binding TotalWarningCount, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock Margin="8,0,4,0" Text="❌" />
                                <TextBlock
                                    FontWeight="SemiBold"
                                    Foreground="Red"
                                    Text="{Binding TotalFailCount, Mode=OneWay, FallbackValue=0}" />
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>

            <!--  Splitter  -->
            <GridSplitter
                Grid.Column="1"
                Width="4"
                HorizontalAlignment="Center"
                VerticalAlignment="Stretch"
                Background="{DynamicResource MaterialDesignDivider}" />

            <!--  Main Content Frame  -->
            <Border
                Grid.Column="2"
                Margin="4,0,0,0"
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="4">
                <Frame
                    x:Name="MainFrame"
                    Margin="8"
                    NavigationUIVisibility="Hidden" />
            </Border>
        </Grid>

        <!--  Status Bar with Progress  -->
        <Border
            Grid.Row="3"
            Padding="16,8"
            Background="{DynamicResource MaterialDesignDivider}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Status Text  -->
                <TextBlock
                    x:Name="StatusText"
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage, FallbackValue=Ready}" />

                <!--  Progress Area  -->
                <StackPanel
                    Grid.Column="1"
                    VerticalAlignment="Center"
                    Orientation="Horizontal"
                    Visibility="{Binding IsProgressVisible, Converter={StaticResource BoolToVisConverter}}">
                    <ProgressBar
                        Width="200"
                        Height="16"
                        Margin="0,0,8,0"
                        Maximum="100"
                        Value="{Binding ProgressValue}" />
                    <TextBlock
                        MinWidth="35"
                        VerticalAlignment="Center"
                        Text="{Binding ProgressValue, StringFormat={}{0:F0}%}" />
                </StackPanel>

                <!--  Busy Indicator  -->
                <materialDesign:Card
                    Grid.Column="1"
                    Padding="8,4"
                    VerticalAlignment="Center"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Visibility="{Binding IsBusy, Converter={StaticResource BoolToVisConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <ProgressBar
                            Width="16"
                            Height="16"
                            Margin="0,0,8,0"
                            IsIndeterminate="True"
                            Style="{StaticResource MaterialDesignCircularProgressBar}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            Foreground="White"
                            Text="Processing..." />
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Border>

    </Grid>
</Window>
