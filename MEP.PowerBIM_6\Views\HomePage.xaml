﻿<Page
    x:Class="MEP.PowerBIM_6.Views.HomePage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="PowerBIM Home"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
        </ResourceDictionary>

    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Welcome Section  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <StackPanel Margin="0,0,0,16" Orientation="Horizontal">
                        <materialDesign:PackIcon
                            Width="32"
                            Height="32"
                            Margin="0,0,12,0"
                            VerticalAlignment="Center"
                            Foreground="{DynamicResource PrimaryHueMidBrush}"
                            Kind="Flash" />
                        <TextBlock
                            VerticalAlignment="Center"
                            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                            Text="Welcome to PowerBIM 6" />
                    </StackPanel>

                    <TextBlock
                        Margin="0,0,0,16"
                        Foreground="{DynamicResource MaterialDesignBodyLight}"
                        Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                        Text="Electrical Circuit Analysis and Cable Sizing Tool" />

                    <TextBlock Margin="0,0,0,16" TextWrapping="Wrap">
                        PowerBIM 6 provides comprehensive electrical circuit analysis for Revit projects.
                        This tool performs automatic cable sizing, voltage drop calculations, and circuit validation
                        to ensure your electrical designs meet safety and performance standards.
                    </TextBlock>

                    <!--  Quick Start Actions  -->
                    <StackPanel Orientation="Horizontal">
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding LoadProjectDataCommand}"
                            Content="Load Project Data" />
                        <Button Command="{Binding HelpCommand}" Content="View Tutorial" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!--  Project Settings Section  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Project Settings" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  Voltage Drop Settings  -->
                        <StackPanel
                            Grid.Row="0"
                            Grid.Column="0"
                            Margin="0,0,16,16">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="System Voltage Drop Maximum:" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton
                                    Margin="0,0,16,0"
                                    Content="5%"
                                    IsChecked="{Binding SystemVD5Percent}" />
                                <RadioButton Content="7%" IsChecked="{Binding SystemVD7Percent}" />
                            </StackPanel>
                        </StackPanel>

                        <!--  Cable Selection  -->
                        <StackPanel
                            Grid.Row="0"
                            Grid.Column="1"
                            Margin="0,0,0,16">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Cable Selection Standard:" />
                            <StackPanel Orientation="Horizontal">
                                <RadioButton
                                    Margin="0,0,16,0"
                                    Content="NZ (30°C)"
                                    IsChecked="{Binding NZCableSelection}" />
                                <RadioButton Content="AUS (40°C)" IsChecked="{Binding AUSCableSelection}" />
                            </StackPanel>
                        </StackPanel>

                        <!--  Project Information  -->
                        <StackPanel
                            Grid.Row="1"
                            Grid.Column="0"
                            Grid.ColumnSpan="2"
                            Margin="0,0,0,16">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Project Information:" />
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <TextBlock
                                    Grid.Column="0"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Text="Job Name:" />
                                <TextBox
                                    Grid.Column="1"
                                    Margin="0,0,16,0"
                                    Text="{Binding ProjectInfo.JobName}" />
                                <TextBlock
                                    Grid.Column="2"
                                    Margin="0,0,8,0"
                                    VerticalAlignment="Center"
                                    Text="Job Number:" />
                                <TextBox Grid.Column="3" Text="{Binding ProjectInfo.JobNumber}" />
                            </Grid>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Project Status Overview  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Project Status" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Total DBs  -->
                        <Border
                            Grid.Column="0"
                            Margin="0,0,8,0"
                            Padding="16"
                            Background="{DynamicResource MaterialDesignDivider}"
                            CornerRadius="4">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    Margin="0,0,0,8"
                                    HorizontalAlignment="Center"
                                    Kind="ViewDashboard" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                    Text="{Binding DistributionBoards.Count, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Text="Distribution Boards"
                                    TextAlignment="Center" />
                            </StackPanel>
                        </Border>

                        <!--  Total Circuits  -->
                        <Border
                            Grid.Column="1"
                            Margin="0,0,8,0"
                            Padding="16"
                            Background="{DynamicResource MaterialDesignDivider}"
                            CornerRadius="4">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    Margin="0,0,0,8"
                                    HorizontalAlignment="Center"
                                    Kind="ElectricSwitch" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                    Text="{Binding TotalCircuitCount, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Text="Total Circuits"
                                    TextAlignment="Center" />
                            </StackPanel>
                        </Border>

                        <!--  Pass Count  -->
                        <Border
                            Grid.Column="2"
                            Margin="0,0,8,0"
                            Padding="16"
                            Background="LightGreen"
                            CornerRadius="4">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    Margin="0,0,0,8"
                                    HorizontalAlignment="Center"
                                    Foreground="Green"
                                    Kind="CheckCircle" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Foreground="Green"
                                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                    Text="{Binding TotalPassCount, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Text="Passing Circuits"
                                    TextAlignment="Center" />
                            </StackPanel>
                        </Border>

                        <!--  Fail Count  -->
                        <Border
                            Grid.Column="3"
                            Padding="16"
                            Background="LightCoral"
                            CornerRadius="4">
                            <StackPanel HorizontalAlignment="Center">
                                <materialDesign:PackIcon
                                    Width="32"
                                    Height="32"
                                    Margin="0,0,0,8"
                                    HorizontalAlignment="Center"
                                    Foreground="Red"
                                    Kind="AlertCircle" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Foreground="Red"
                                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                    Text="{Binding TotalFailCount, Mode=OneWay, FallbackValue=0}" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    Text="Failed Circuits"
                                    TextAlignment="Center" />
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Recent Actions  -->
            <materialDesign:Card Padding="16">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Quick Actions" />

                    <UniformGrid Columns="3" Rows="2">
                        <Button
                            Margin="4"
                            Command="{Binding LoadProjectDataCommand}"
                            Content="Load Project Data" />
                        <Button
                            Margin="4"
                            Command="{Binding RunAutoSizerCommand}"
                            Content="Run Auto Sizer" />
                        <Button
                            Margin="4"
                            Command="{Binding SaveCommand}"
                            Content="Save Results" />
                        <Button
                            Margin="4"
                            Command="{Binding ExportCommand}"
                            Content="Export Data" />
                        <Button Margin="4" Content="View Settings" />
                        <Button
                            Margin="4"
                            Command="{Binding HelpCommand}"
                            Content="Help and Support" />
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </ScrollViewer>
</Page>
