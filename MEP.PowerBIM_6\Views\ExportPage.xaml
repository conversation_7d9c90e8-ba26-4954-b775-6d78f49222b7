<Page
    x:Class="MEP.PowerBIM_6.Views.ExportPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Export Data"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="16">
            <!--  Page Header  -->
            <StackPanel Margin="0,0,0,24">
                <TextBlock
                    Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                    Text="Export Data" />
                <TextBlock
                    Margin="0,4,0,0"
                    Foreground="{DynamicResource MaterialDesignBodyLight}"
                    Text="Export PowerBIM data to various formats" />
            </StackPanel>

            <!--  Export Options  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Export Options" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Data Export  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Data Export" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportToExcelCommand}"
                                Content="Export to Excel" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportToCsvCommand}"
                                Content="Export to CSV" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportToPdfCommand}"
                                Content="Export to PDF Report" />

                            <Button
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportToXmlCommand}"
                                Content="Export to XML" />
                        </StackPanel>

                        <!--  Visual Export  -->
                        <StackPanel Grid.Column="1">
                            <TextBlock
                                Margin="0,0,0,12"
                                FontWeight="SemiBold"
                                Text="Visual Export" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportCircuitImagesCommand}"
                                Content="Export Circuit Images" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportPathImagesCommand}"
                                Content="Export Path Images" />

                            <Button
                                Margin="0,0,0,8"
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportSchedulesCommand}"
                                Content="Export Circuit Schedules" />

                            <Button
                                HorizontalAlignment="Stretch"
                                Command="{Binding ExportDiagramsCommand}"
                                Content="Export Electrical Diagrams" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Export Settings  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Export Settings" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  Content Selection  -->
                        <StackPanel Grid.Column="0" Margin="0,0,16,0">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Content to Export:" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Distribution Board Summary"
                                IsChecked="{Binding ExportDistributionBoards}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Circuit Details"
                                IsChecked="{Binding ExportCircuitDetails}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Cable Specifications"
                                IsChecked="{Binding ExportCableSpecs}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Protection Settings"
                                IsChecked="{Binding ExportProtectionSettings}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Calculation Results"
                                IsChecked="{Binding ExportCalculationResults}" />

                            <CheckBox
                                Content="Project Settings"
                                IsChecked="{Binding ExportProjectSettings}" />
                        </StackPanel>

                        <!--  Format Options  -->
                        <StackPanel Grid.Column="1">
                            <TextBlock
                                Margin="0,0,0,8"
                                FontWeight="SemiBold"
                                Text="Format Options:" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Include Headers"
                                IsChecked="{Binding IncludeHeaders}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Include Units"
                                IsChecked="{Binding IncludeUnits}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Include Formulas (Excel)"
                                IsChecked="{Binding IncludeFormulas}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Color Code Results"
                                IsChecked="{Binding ColorCodeResults}" />

                            <CheckBox
                                Margin="0,0,0,4"
                                Content="Include Charts"
                                IsChecked="{Binding IncludeCharts}" />

                            <CheckBox
                                Content="Compress Output"
                                IsChecked="{Binding CompressOutput}" />
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!--  Output Location  -->
            <materialDesign:Card Margin="0,0,0,16" Padding="24">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Output Location" />

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <TextBox
                            Grid.Column="0"
                            Margin="0,0,8,0"
                            materialDesign:HintAssist.Hint="Export folder path"
                            Text="{Binding ExportPath}" />

                        <Button
                            Grid.Column="1"
                            Command="{Binding BrowseExportPathCommand}"
                            Content="Browse..." />
                    </Grid>

                    <TextBox
                        Margin="0,16,0,0"
                        materialDesign:HintAssist.Hint="File name prefix"
                        Text="{Binding FileNamePrefix}" />
                </StackPanel>
            </materialDesign:Card>

            <!--  Export Progress  -->
            <materialDesign:Card Padding="24" Visibility="{Binding IsExporting, Converter={StaticResource BoolToVisConverter}}">
                <StackPanel>
                    <TextBlock
                        Margin="0,0,0,16"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="Export Progress" />

                    <TextBlock
                        Margin="0,0,0,8"
                        Text="{Binding ExportStatus}" />

                    <ProgressBar
                        Height="20"
                        Margin="0,0,0,8"
                        Maximum="100"
                        Value="{Binding ExportProgress}" />

                    <TextBlock
                        HorizontalAlignment="Center"
                        Text="{Binding ExportProgress, StringFormat='{}{0:F0}% Complete'}" />
                </StackPanel>
            </materialDesign:Card>

            <!--  Action Buttons  -->
            <StackPanel Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding StartExportCommand}"
                    Content="Start Export"
                    IsEnabled="{Binding CanStartExport}" />

                <Button
                    Margin="0,0,8,0"
                    Command="{Binding CancelExportCommand}"
                    Content="Cancel Export"
                    IsEnabled="{Binding IsExporting}" />

                <Button
                    Command="{Binding OpenExportFolderCommand}"
                    Content="Open Export Folder"
                    IsEnabled="{Binding HasExportedFiles}" />
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</Page>
