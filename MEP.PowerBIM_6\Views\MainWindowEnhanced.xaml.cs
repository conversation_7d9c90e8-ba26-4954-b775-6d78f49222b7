using System;
using System.Windows;
using System.Windows.Controls;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.Services.Interfaces;
using MEP.PowerBIM_6.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using ListBox = System.Windows.Controls.ListBox;
using MessageBox = System.Windows.MessageBox;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for MainWindowEnhanced.xaml
    /// Enhanced main window with page-based navigation
    /// </summary>
    public partial class MainWindowEnhanced : Window
    {
        #region Fields

        private INavigationService _navigationService;
        private IServiceProvider _serviceProvider;
        private bool _isUpdatingSelection = false; // Flag to prevent circular calls

        #endregion

        #region Constructor

        /// <summary>
        /// Initialize the enhanced main window
        /// </summary>
        public MainWindowEnhanced()
        {
            InitializeComponent();
            Loaded += OnWindowLoaded;
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle window loaded event
        /// </summary>
        private void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Get services from DataContext (MainViewModel should have ServiceProvider)
                if (DataContext is MainViewModel mainViewModel)
                {
                    _serviceProvider = mainViewModel.ServiceProvider;
                    _navigationService = _serviceProvider?.GetService<INavigationService>();
                    
                    if (_navigationService != null)
                    {
                        // Initialize navigation service with the main frame
                        _navigationService.Initialize(MainFrame);
                        
                        // Navigate to home page by default
                        _navigationService.NavigateTo(PageKeys.Home);
                        
                        // Subscribe to navigation events
                        _navigationService.Navigated += OnNavigated;
                        _navigationService.NavigationFailed += OnNavigationFailed;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to initialize navigation: {ex.Message}", "Navigation Error", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle window closing event
        /// </summary>
        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // Unsubscribe from navigation events
                if (_navigationService != null)
                {
                    _navigationService.Navigated -= OnNavigated;
                    _navigationService.NavigationFailed -= OnNavigationFailed;
                }

                // Allow the window to close
                // The ModelessMainWindowHandler will handle cleanup
            }
            catch (Exception ex)
            {
                // Log error but don't prevent closing
                System.Diagnostics.Debug.WriteLine($"Error during window closing: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle navigation list selection changed
        /// </summary>
        private void NavigationList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // Prevent circular calls
                if (_isUpdatingSelection)
                    return;

                if (sender is ListBox listBox && listBox.SelectedItem is ListBoxItem selectedItem)
                {
                    string pageKey = selectedItem.Name switch
                    {
                        "navHome" => PageKeys.Home,
                        "navDistributionBoards" => PageKeys.DistributionBoards,
                        "navCircuits" => PageKeys.Circuits,
                        "navResults" => PageKeys.BulkOperations,
                        "navSettings" => PageKeys.ProjectSettings,
                        _ => PageKeys.Home
                    };

                    _navigationService?.NavigateTo(pageKey);
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Navigation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Navigate to home page
        /// </summary>
        private void NavigateToHome(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_navigationService?.NavigateTo(PageKeys.Home) == true)
                {
                    UpdateNavigationSelection("navHome");
                }
            }
            catch (Exception ex)
            {
                ShowNavigationError($"Failed to navigate to home: {ex.Message}");
            }
        }

        /// <summary>
        /// Open Help and Documentation dialog
        /// </summary>
        private void OpenHelpDialog(object sender, RoutedEventArgs e)
        {
            try
            {
                var helpDialog = new PowerBIMHelpDialog();
                helpDialog.Owner = this;
                helpDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open Help dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Open About window
        /// </summary>
        private void OpenAboutWindow(object sender, RoutedEventArgs e)
        {
            try
            {
                var aboutWindow = new PowerBIMAboutWindow();
                aboutWindow.Owner = this;
                aboutWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open About window: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Handle successful navigation
        /// </summary>
        private void OnNavigated(object sender, System.Windows.Navigation.NavigationEventArgs e)
        {
            try
            {
                // Navigation completed successfully - no additional UI updates needed
                // to prevent circular calls with UpdateNavigationHighlight
                System.Diagnostics.Debug.WriteLine($"Navigation completed to: {e.Uri}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling navigation event: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle navigation failure
        /// </summary>
        private void OnNavigationFailed(object sender, MEP.PowerBIM_6.Services.NavigationFailedEventArgs e)
        {
            ShowNavigationError($"Navigation failed: {e.Exception?.Message ?? "Unknown error"}");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update navigation selection to match current page
        /// </summary>
        /// <param name="selectedItemName">Name of the item to select</param>
        private void UpdateNavigationSelection(string selectedItemName)
        {
            try
            {
                // Set flag to prevent circular calls
                _isUpdatingSelection = true;

                // Clear current selection
                NavigationList.SelectedItem = null;

                // Find and select the appropriate item
                foreach (ListBoxItem item in NavigationList.Items)
                {
                    if (item.Name == selectedItemName)
                    {
                        item.IsSelected = true;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating navigation selection: {ex.Message}");
            }
            finally
            {
                // Always reset the flag
                _isUpdatingSelection = false;
            }
        }

        /// <summary>
        /// Update navigation highlight based on current page
        /// </summary>
        private void UpdateNavigationHighlight()
        {
            try
            {
                if (_navigationService?.CurrentPageKey != null)
                {
                    string itemName = _navigationService.CurrentPageKey switch
                    {
                        PageKeys.Home => "navHome",
                        PageKeys.DistributionBoards => "navDistributionBoards",
                        PageKeys.Circuits => "navCircuits",
                        PageKeys.BulkOperations => "navResults",
                        PageKeys.ProjectSettings => "navSettings",
                        _ => "navHome"
                    };

                    UpdateNavigationSelection(itemName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating navigation highlight: {ex.Message}");
            }
        }

        /// <summary>
        /// Show navigation error message
        /// </summary>
        /// <param name="message">Error message</param>
        private void ShowNavigationError(string message)
        {
            try
            {
                MessageBox.Show(message, "Navigation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch
            {
                // Fallback if MessageBox fails
                System.Diagnostics.Debug.WriteLine($"Navigation Error: {message}");
            }
        }

        #endregion
    }
}
