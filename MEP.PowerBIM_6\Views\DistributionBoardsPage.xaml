<Page
    x:Class="MEP.PowerBIM_6.Views.DistributionBoardsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Distribution Boards"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
        </ResourceDictionary>
    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Page Header  -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock
                Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                Text="Distribution Boards" />
            <TextBlock
                Margin="0,4,0,0"
                Foreground="{DynamicResource MaterialDesignBodyLight}"
                Text="Manage electrical distribution boards and their circuits" />
        </StackPanel>

        <!--  Action Toolbar  -->
        <Border
            Grid.Row="1"
            Margin="0,0,0,16"
            Padding="12"
            Background="{DynamicResource MaterialDesignDivider}"
            CornerRadius="4">
            <StackPanel Orientation="Horizontal">
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding RefreshDataCommand}"
                    Content="Refresh Data"
                    ToolTip="Refresh distribution board data from Revit" />
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding RunCalculationsCommand}"
                    Content="Run Calculations"
                    ToolTip="Run calculations for all distribution boards" />
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding SaveCommand}"
                    Content="Save Changes"
                    ToolTip="Save changes to Revit" />
                <Separator Margin="8,0" />
                <Button
                    Margin="0,0,8,0"
                    Command="{Binding ExportCommand}"
                    Content="Export"
                    ToolTip="Export distribution board data" />
            </StackPanel>
        </Border>

        <!--  Distribution Boards List  -->
        <materialDesign:Card Grid.Row="2" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Search and Filter  -->
                <Grid Grid.Row="0" Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBox
                        Grid.Column="0"
                        Margin="0,0,8,0"
                        materialDesign:HintAssist.Hint="Search distribution boards..."
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}" />
                    
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Failed"
                            IsChecked="{Binding ShowOnlyFailed}" />
                        <CheckBox
                            Margin="8,0"
                            Content="Show Only Locked"
                            IsChecked="{Binding ShowOnlyLocked}" />
                    </StackPanel>
                </Grid>

                <!--  Distribution Boards DataGrid  -->
                <DataGrid
                    Grid.Row="1"
                    AutoGenerateColumns="False"
                    CanUserAddRows="False"
                    CanUserDeleteRows="False"
                    ItemsSource="{Binding DistributionBoards}"
                    SelectedItem="{Binding SelectedDistributionBoard}"
                    Style="{StaticResource MaterialDesignDataGrid}">
                    
                    <DataGrid.Columns>
                        <!--  Selection Column  -->
                        <DataGridCheckBoxColumn
                            Width="50"
                            Binding="{Binding IsSelected}"
                            Header="Select" />

                        <!--  Status Indicator  -->
                        <DataGridTemplateColumn Width="60" Header="Status">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        HorizontalAlignment="Center"
                                        Foreground="{Binding Status, Converter={StaticResource StatusColorConverter}}"
                                        Kind="{Binding StatusIcon}" />
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!--  Distribution Board Name  -->
                        <DataGridTextColumn
                            Width="200"
                            Binding="{Binding Name}"
                            Header="Distribution Board Name" />

                        <!--  Description  -->
                        <DataGridTextColumn
                            Width="250"
                            Binding="{Binding Description}"
                            Header="Description" />

                        <!--  Circuit Count  -->
                        <DataGridTextColumn
                            Width="100"
                            Binding="{Binding CircuitCount}"
                            Header="Circuits" />

                        <!--  Pass Count  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding PassCount}"
                            Header="Pass" />

                        <!--  Fail Count  -->
                        <DataGridTextColumn
                            Width="80"
                            Binding="{Binding FailCount}"
                            Header="Fail" />

                        <!--  Total Load  -->
                        <DataGridTextColumn
                            Width="100"
                            Binding="{Binding TotalLoad, StringFormat=F2}"
                            Header="Total Load (A)" />

                        <!--  Locked Status  -->
                        <DataGridCheckBoxColumn
                            Width="80"
                            Binding="{Binding IsLocked}"
                            Header="Locked" />

                        <!--  Actions  -->
                        <DataGridTemplateColumn Width="120" Header="Actions">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button
                                            Margin="2"
                                            Command="{Binding DataContext.EditDistributionBoardCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                            CommandParameter="{Binding}"
                                            Content="Edit"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            ToolTip="Edit distribution board" />
                                        <Button
                                            Margin="2"
                                            Command="{Binding DataContext.ViewCircuitsCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                            CommandParameter="{Binding}"
                                            Content="Circuits"
                                            Style="{StaticResource MaterialDesignFlatButton}"
                                            ToolTip="View circuits" />
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
